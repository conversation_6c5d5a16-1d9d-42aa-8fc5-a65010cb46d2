<?php
// Učitaj konfiguraciju
require_once 'email_config.php';

// Funkcija za slanje emaila
function sendEmail($to, $subject, $message, $headers) {
    if (USE_SMTP) {
        // SMTP slanje (potrebna je PHPMailer biblioteka)
        // Ova funkcionalnost zahteva instalaciju PHPMailer-a
        // composer require phpmailer/phpmailer

        /*
        use PHPMailer\PHPMailer\PHPMailer;
        use PHPMailer\PHPMailer\SMTP;
        use PHPMailer\PHPMailer\Exception;

        require 'vendor/autoload.php';

        $mail = new PHPMailer(true);

        try {
            $mail->isSMTP();
            $mail->Host       = SMTP_HOST;
            $mail->SMTPAuth   = true;
            $mail->Username   = SMTP_USERNAME;
            $mail->Password   = SMTP_PASSWORD;
            $mail->SMTPSecure = SMTP_SECURE;
            $mail->Port       = SMTP_PORT;
            $mail->CharSet    = 'UTF-8';

            $mail->setFrom(FROM_EMAIL, 'Collagen Honey');
            $mail->addAddress($to);

            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body    = $message;

            $mail->send();
            return true;
        } catch (Exception $e) {
            error_log("SMTP Error: {$mail->ErrorInfo}");
            return false;
        }
        */

        // Za sada vraćamo false jer SMTP nije implementiran
        return false;
    } else {
        // Koristi PHP mail() funkciju
        return mail($to, $subject, $message, $headers);
    }
}

// Proveri da li je forma poslata POST metodom
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Sanitizuj i validuj podatke
    $ime_prezime = isset($_POST['Poručuje']) ? htmlspecialchars(trim($_POST['Poručuje'])) : '';
    $adresa = isset($_POST['Adresa']) ? htmlspecialchars(trim($_POST['Adresa'])) : '';
    $grad = isset($_POST['Grad']) ? htmlspecialchars(trim($_POST['Grad'])) : '';
    $postanski_broj = isset($_POST['PoštanskiBroj']) ? htmlspecialchars(trim($_POST['PoštanskiBroj'])) : '';
    $telefon = isset($_POST['Telefon']) ? htmlspecialchars(trim($_POST['Telefon'])) : '';
    $email = isset($_POST['Email']) ? htmlspecialchars(trim($_POST['Email'])) : '';
    $napomena = isset($_POST['textarea']) ? htmlspecialchars(trim($_POST['textarea'])) : '';
    $kolicina = isset($_POST['Količina']) ? intval($_POST['Količina']) : 1;
    $potvrda_podataka = isset($_POST['PotvrdaPodataka']) ? 'Da' : 'Ne';
    
    // Validacija obaveznih polja
    $errors = array();

    if (empty($ime_prezime)) {
        $errors[] = $validation_messages['ime_prezime_required'];
    }

    if (empty($adresa)) {
        $errors[] = $validation_messages['adresa_required'];
    }

    if (empty($grad)) {
        $errors[] = $validation_messages['grad_required'];
    }

    if (empty($postanski_broj)) {
        $errors[] = $validation_messages['postanski_broj_required'];
    }

    if (empty($telefon)) {
        $errors[] = $validation_messages['telefon_required'];
    }

    if ($kolicina < 1) {
        $errors[] = $validation_messages['kolicina_invalid'];
    }

    if ($potvrda_podataka !== 'Da') {
        $errors[] = $validation_messages['potvrda_required'];
    }

    // Validacija email adrese ako je uneta
    if (ENABLE_EMAIL_VALIDATION && !empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = $validation_messages['email_invalid'];
    }
    
    // Ako nema grešaka, pošalji email
    if (empty($errors)) {
        
        // Izračunaj ukupnu cenu
        $cena_po_kutiji = PRICE_PER_BOX;
        $ukupna_cena = $kolicina * $cena_po_kutiji;
        
        // Kreiraj sadržaj emaila
        $message = "
        <html>
        <head>
            <title>Nova porudžbina - Collagen Honey</title>
            <style>
                body { font-family: Arial, sans-serif; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #d9a832; color: white; padding: 15px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .field { margin-bottom: 10px; }
                .label { font-weight: bold; color: #333; }
                .value { color: #666; }
                .total { background-color: #d9a832; color: white; padding: 10px; text-align: center; font-size: 18px; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>Nova porudžbina - Collagen Honey</h2>
                </div>
                <div class='content'>
                    <div class='field'>
                        <span class='label'>Ime i prezime:</span> 
                        <span class='value'>$ime_prezime</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Adresa:</span> 
                        <span class='value'>$adresa</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Grad:</span> 
                        <span class='value'>$grad</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Poštanski broj:</span> 
                        <span class='value'>$postanski_broj</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Telefon:</span> 
                        <span class='value'>$telefon</span>
                    </div>";
        
        if (!empty($email)) {
            $message .= "
                    <div class='field'>
                        <span class='label'>Email:</span> 
                        <span class='value'>$email</span>
                    </div>";
        }
        
        if (!empty($napomena)) {
            $message .= "
                    <div class='field'>
                        <span class='label'>Napomena:</span> 
                        <span class='value'>$napomena</span>
                    </div>";
        }
        
        $message .= "
                    <div class='field'>
                        <span class='label'>Broj kutija:</span> 
                        <span class='value'>$kolicina</span>
                    </div>
                    <div class='field'>
                        <span class='label'>Cena po kutiji:</span> 
                        <span class='value'>$cena_po_kutiji RSD</span>
                    </div>
                </div>
                <div class='total'>
                    UKUPNO: $ukupna_cena RSD
                </div>
                <div style='padding: 15px; text-align: center; color: #666; font-size: 12px;'>
                    Porudžbina je stigla sa sajta collagenhoney.com<br>
                    Datum: " . date('d.m.Y H:i:s') . "
                </div>
            </div>
        </body>
        </html>";
        
        // Email headers optimizovani za Outlook
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8" . "\r\n";
        $headers .= "Content-Transfer-Encoding: 8bit" . "\r\n";
        $headers .= "From: Collagen Honey <" . FROM_EMAIL . ">" . "\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        $headers .= "X-Priority: 3" . "\r\n";
        $headers .= "X-MSMail-Priority: Normal" . "\r\n";
        $headers .= "Importance: Normal" . "\r\n";

        if (!empty($email)) {
            $headers .= "Reply-To: $ime_prezime <$email>" . "\r\n";
        } else {
            $headers .= "Reply-To: Collagen Honey <" . REPLY_TO_EMAIL . ">" . "\r\n";
        }

        // Logiraj porudžbinu ako je omogućeno
        if (ENABLE_LOGGING) {
            $log_entry = "=== NOVA PORUDŽBINA ===\n";
            $log_entry .= "Datum i vreme: " . date('d.m.Y H:i:s') . "\n";
            $log_entry .= "Ime i prezime: $ime_prezime\n";
            $log_entry .= "Adresa: $adresa\n";
            $log_entry .= "Grad: $grad\n";
            $log_entry .= "Poštanski broj: $postanski_broj\n";
            $log_entry .= "Telefon: $telefon\n";

            if (!empty($email)) {
                $log_entry .= "Email: $email\n";
            }

            if (!empty($napomena)) {
                $log_entry .= "Napomena: $napomena\n";
            }

            $log_entry .= "Broj kutija: $kolicina\n";
            $log_entry .= "Cena po kutiji: $cena_po_kutiji RSD\n";
            $log_entry .= "UKUPNA CENA: $ukupna_cena RSD\n";
            $log_entry .= "Potvrda podataka: $potvrda_podataka\n";

            // Dodaj dodatne informacije ako je omogućeno detaljno logovanje
            if (defined('DETAILED_LOGGING') && DETAILED_LOGGING) {
                $log_entry .= "IP adresa: " . ($_SERVER['REMOTE_ADDR'] ?? 'N/A') . "\n";
                $log_entry .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A') . "\n";
                $log_entry .= "Referer: " . ($_SERVER['HTTP_REFERER'] ?? 'N/A') . "\n";

                // Dodaj informacije o browseru ako su dostupne
                if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
                    $log_entry .= "Jezik: " . $_SERVER['HTTP_ACCEPT_LANGUAGE'] . "\n";
                }
            }

            $log_entry .= "========================\n\n";

            file_put_contents(LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
        }

        // Pošalji email
        if (sendEmail(TO_EMAIL, EMAIL_SUBJECT, $message, $headers)) {
            // Uspešno poslato - preusmeri na success stranicu
            header("Location: Poručivanje.html?status=success");
            exit();
        } else {
            // Greška pri slanju
            header("Location: Poručivanje.html?status=error&msg=" . urlencode($validation_messages['email_send_error']));
            exit();
        }
        
    } else {
        // Ima grešaka - preusmeri sa greškama
        $error_msg = implode(" ", $errors);
        header("Location: Poručivanje.html?status=error&msg=" . urlencode($error_msg));
        exit();
    }
    
} else {
    // Nije POST metoda
    header("Location: Poručivanje.html");
    exit();
}
?>
